"""
 Copyright (c) 2023, salesforce.com, inc.
 All rights reserved.
 SPDX-License-Identifier: BSD-3-Clause
 For full license text, see the LICENSE file in the repo root or https://opensource.org/licenses/BSD-3-Clause
"""
import logging

import torch
import torch.nn as nn
from torch.cuda.amp import autocast as autocast
from torch.nn import functional as F

from lavis.common.registry import registry
from lavis.models.blip2_models.blip2 import (
    Blip2Base,
    disabled_train,
)
from utility import get_closs
import numpy as np

@registry.register_model("blip2_cir_image_diff_features")
class Blip2QformerCirImageDiffFeatures(Blip2Base):
    """
    model with Q-former and ViT based on BLIP2.
    Usage:
        >>> from lavis.models import load_model
        >>> model = load_model("blip2_cir_image_diff_features", "pretrain")
    """

    PRETRAINED_MODEL_CONFIG_DICT = {
        "pretrain": "configs/models/blip2/blip2_pretrain.yaml",
        "pretrain_vitL": "configs/models/blip2/blip2_pretrain_vitL.yaml",
        "coco": "configs/models/blip2/blip2_coco.yaml",
    }

    def __init__(
        self,
        vit_model="eva_clip_g",
        img_size=224,
        drop_path_rate=0,
        use_grad_checkpoint=False,
        vit_precision="fp16",
        freeze_vit=True,
        num_query_token=32,
        cross_attention_freq=2,
        embed_dim=256,
        max_txt_len=32,
        # AFCE相关参数
        afce_mode="FCE",  # "CE", "FCE", "AFCE_fixed", "AFCE_dynamic"
        afce_p=0.5,  # 固定p模式的参数
        afce_alpha=2.0,  # 动态p模式的Beta分布参数
        afce_beta=2.0,   # 动态p模式的Beta分布参数
    ):
        super().__init__()

        self.tokenizer = self.init_tokenizer()

        self.visual_encoder, self.ln_vision = self.init_vision_encoder(
            vit_model, img_size, drop_path_rate, use_grad_checkpoint, vit_precision
        )
        if freeze_vit:
            for name, param in self.visual_encoder.named_parameters():
                param.requires_grad = False
            self.visual_encoder = self.visual_encoder.eval()
            self.visual_encoder.train = disabled_train
            logging.info("freeze vision encoder")
        self.Qformer, self.query_tokens = self.init_Qformer(
            num_query_token, self.visual_encoder.num_features, cross_attention_freq
        )
        self.Qformer.resize_token_embeddings(len(self.tokenizer))
        state_dict = self.Qformer.state_dict()
        for name, param in self.Qformer.named_parameters():
            if "_query" in name:
                key_orig = name.replace("_query", "")
                param.data.copy_(state_dict[key_orig])

        self.vision_proj = nn.Linear(self.Qformer.config.hidden_size, embed_dim)
        self.text_proj = nn.Linear(self.Qformer.config.hidden_size, embed_dim)

        self.itm_head = nn.Linear(self.Qformer.config.hidden_size, 2)

        self.temp = nn.Parameter(0.07 * torch.ones([]))

        self.max_txt_len = max_txt_len
        
        # AFCE相关参数
        self.afce_mode = afce_mode
        self.afce_p = afce_p
        self.afce_alpha = afce_alpha
        self.afce_beta = afce_beta
    
    @torch.no_grad()
    def vit_encode(self, image):
        return self.visual_encoder(image)
    
    # Image Encoder
    def encode_image(self, image_embeds, query_tokens=None, ln=True):
        """ Encode images.
        Args:
            image_embeds (Tensor): Image representations encoded by ViT.
            query_tokens (Tensor): The query tokens of Q-Former.
            ln (Tensor): whether to perform layer norm.
        Returns:
            Tensor: Image representation encoded by Qformer.
        """
        if ln:
            with self.maybe_autocast():
                image_embeds = self.ln_vision(image_embeds)
        if query_tokens is None:
            query_tokens = self.query_tokens.expand(image_embeds.shape[0], -1, -1)
        image_atts = torch.ones(image_embeds.size()[:-1], dtype=torch.long).to(
            image_embeds.device
        )
        image_output = self.Qformer.bert(
            query_embeds=query_tokens,
            encoder_hidden_states=image_embeds,
            encoder_attention_mask=image_atts,
            return_dict=True,
        )
        return image_output.last_hidden_state
    
    # Text Encoder - 新增方法用于单独编码文本
    def encode_text(self, text_tokens):
        """单独编码文本"""
        text_output = self.Qformer.bert(
            text_tokens.input_ids,
            attention_mask=text_tokens.attention_mask,
            return_dict=True,
        )
        return F.normalize(self.text_proj(text_output.last_hidden_state[:, 0, :]), dim=-1)
    
    # Fusion Encoder
    def encode_fusion(self, F_image, text_tokens, no_image=False):
        """Fuse image representations with texts.
        
        Args:
            F_image (Tensor): Image representation
            text_tokens (Tensor): text_tokens
            no_image (bool, optional): no_image is True if F_image is prompt tokens, Defaults to False.
        """
        bs = text_tokens.input_ids.shape[0]
        image_atts = torch.ones(F_image.shape[:-1], dtype=torch.long).to(
            F_image.device
        )
        attention_mask = torch.cat([image_atts, text_tokens.attention_mask], dim=1)
        assert F_image.shape[:-1] == (bs, 32)
        fusion_output = self.Qformer.bert(
            text_tokens.input_ids,
            query_embeds=F_image,
            attention_mask=attention_mask,
            return_dict=True,
            no_img=no_image,
        )
        token_num = 0 if no_image else 32
        res = F.normalize(self.text_proj(fusion_output.last_hidden_state[:, token_num, :]), dim=-1)
        return res
    
    def create_synthetic_composition_examples(self, F_reference, text_embeds, batch_size):
        """
        创建合成组合样本 (FCE)
        
        Args:
            F_reference: 参考图像特征 [B, D]
            text_embeds: 文本嵌入 [B, D]
            batch_size: 批次大小
            
        Returns:
            synthetic_compositions: 合成组合样本 [B*(B-1), D]
        """
        synthetic_compositions = []
        
        for i in range(batch_size):
            for j in range(batch_size):
                if i != j:
                    # f'_j = r_i + s_j (使用第i个图像和第j个文本)
                    synthetic_comp = F_reference[i] + text_embeds[j]
                    synthetic_compositions.append(synthetic_comp)
        
        return torch.stack(synthetic_compositions)
    
    def create_augmented_synthetic_composition_examples(self, F_reference, text_embeds, batch_size):
        """
        创建增强合成组合样本 (AFCE)
        
        Args:
            F_reference: 参考图像特征 [B, D]
            text_embeds: 文本嵌入 [B, D]
            batch_size: 批次大小
            
        Returns:
            augmented_compositions: 增强合成组合样本 [B*(B-1), D]
        """
        augmented_compositions = []
        embed_dim = text_embeds.shape[1]
        
        for i in range(batch_size):
            for j in range(batch_size):
                if i != j:
                    # 创建掩码向量
                    if self.afce_mode == "AFCE_fixed":
                        # 固定p模式
                        p = self.afce_p
                    elif self.afce_mode == "AFCE_dynamic":
                        # 动态p模式：从Beta分布采样
                        p = np.random.beta(self.afce_alpha, self.afce_beta)
                    else:
                        p = self.afce_p
                    
                    # 创建伯努利掩码
                    mask = torch.bernoulli(torch.full((embed_dim,), p)).to(text_embeds.device)
                    
                    # 元素级别增强: s_tilde = m ⊙ s_i + (1-m) ⊙ s_j
                    augmented_text = mask * text_embeds[i] + (1 - mask) * text_embeds[j]
                    
                    # f_tilde'_j = r_i + s_tilde
                    augmented_comp = F_reference[i] + augmented_text
                    augmented_compositions.append(augmented_comp)
        
        return torch.stack(augmented_compositions)
    
    def compute_afce_loss(self, z_rm, z_target, F_reference, text_embeds, labels):
        """
        计算AFCE损失
        
        Args:
            z_rm: 正样本查询嵌入 [B, D]
            z_target: 目标图像嵌入 [B, D]
            F_reference: 参考图像特征 [B, D]
            text_embeds: 文本嵌入 [B, D]
            labels: 样本标签
            
        Returns:
            loss_dict: 包含各种损失的字典
        """
        batch_size = z_rm.shape[0]
        loss_dict = {}
        
        if self.afce_mode == "CE":
            # 原始CE方法：使用完整查询作为负样本
            sim_matrix = torch.matmul(z_rm, z_target.t())
            
            # L1损失：以目标为锚点
            positive_sim = sim_matrix.diag()
            negative_sims = sim_matrix + torch.eye(batch_size).to(sim_matrix.device) * (-1e9)
            logits_l1 = torch.cat([positive_sim.unsqueeze(1), negative_sims], dim=1)
            targets_l1 = torch.zeros(batch_size, dtype=torch.long).to(sim_matrix.device)
            loss_l1 = F.cross_entropy(logits_l1 / self.temp, targets_l1)
            
            # L0损失：以查询为锚点
            logits_l0 = torch.cat([positive_sim.unsqueeze(1), negative_sims.t()], dim=1)
            targets_l0 = torch.zeros(batch_size, dtype=torch.long).to(sim_matrix.device)
            loss_l0 = F.cross_entropy(logits_l0 / self.temp, targets_l0)
            
            total_loss = (loss_l1 + loss_l0) / 2
            
        elif self.afce_mode == "FCE":
            # FCE方法：使用合成组合样本
            synthetic_comps = self.create_synthetic_composition_examples(F_reference, text_embeds, batch_size)

            # 合成组合样本已经在投影空间中，直接归一化
            synthetic_query_embeds = F.normalize(synthetic_comps, dim=-1)
            
            # L2损失：以目标为锚点，使用合成组合负样本
            positive_sim = torch.matmul(z_rm, z_target.t()).diag()
            negative_sims = []
            
            idx = 0
            for i in range(batch_size):
                neg_sims_for_target_i = []
                for j in range(batch_size):
                    if i != j:
                        # 计算目标i与合成样本的相似度
                        sim = torch.dot(z_target[i], synthetic_query_embeds[idx])
                        neg_sims_for_target_i.append(sim)
                        idx += 1
                negative_sims.append(torch.stack(neg_sims_for_target_i))
            
            negative_sims = torch.stack(negative_sims)  # [B, B-1]
            
            # 构建logits：[正样本相似度, 负样本相似度们]
            logits_l2 = torch.cat([positive_sim.unsqueeze(1), negative_sims], dim=1)
            targets_l2 = torch.zeros(batch_size, dtype=torch.long).to(logits_l2.device)
            loss_l2 = F.cross_entropy(logits_l2 / self.temp, targets_l2)
            
            # L0损失保持不变
            sim_matrix = torch.matmul(z_rm, z_target.t())
            negative_sims_l0 = sim_matrix + torch.eye(batch_size).to(sim_matrix.device) * (-1e9)
            logits_l0 = torch.cat([positive_sim.unsqueeze(1), negative_sims_l0.t()], dim=1)
            targets_l0 = torch.zeros(batch_size, dtype=torch.long).to(sim_matrix.device)
            loss_l0 = F.cross_entropy(logits_l0 / self.temp, targets_l0)
            
            total_loss = (loss_l2 + loss_l0) / 2
            
        elif self.afce_mode in ["AFCE_fixed", "AFCE_dynamic"]:
            # AFCE方法：使用增强合成组合样本
            augmented_comps = self.create_augmented_synthetic_composition_examples(F_reference, text_embeds, batch_size)

            # 增强合成组合样本已经在投影空间中，直接归一化
            augmented_query_embeds = F.normalize(augmented_comps, dim=-1)
            
            # L3损失：以目标为锚点，使用增强合成组合负样本
            positive_sim = torch.matmul(z_rm, z_target.t()).diag()
            negative_sims = []
            
            idx = 0
            for i in range(batch_size):
                neg_sims_for_target_i = []
                for j in range(batch_size):
                    if i != j:
                        # 计算目标i与增强合成样本的相似度
                        sim = torch.dot(z_target[i], augmented_query_embeds[idx])
                        neg_sims_for_target_i.append(sim)
                        idx += 1
                negative_sims.append(torch.stack(neg_sims_for_target_i))
            
            negative_sims = torch.stack(negative_sims)  # [B, B-1]
            
            # 构建logits
            logits_l3 = torch.cat([positive_sim.unsqueeze(1), negative_sims], dim=1)
            targets_l3 = torch.zeros(batch_size, dtype=torch.long).to(logits_l3.device)
            loss_l3 = F.cross_entropy(logits_l3 / self.temp, targets_l3)
            
            # L0损失保持不变
            sim_matrix = torch.matmul(z_rm, z_target.t())
            negative_sims_l0 = sim_matrix + torch.eye(batch_size).to(sim_matrix.device) * (-1e9)
            logits_l0 = torch.cat([positive_sim.unsqueeze(1), negative_sims_l0.t()], dim=1)
            targets_l0 = torch.zeros(batch_size, dtype=torch.long).to(sim_matrix.device)
            loss_l0 = F.cross_entropy(logits_l0 / self.temp, targets_l0)
            
            total_loss = (loss_l3 + loss_l0) / 2
            
        else:
            raise ValueError(f"Invalid AFCE mode: {self.afce_mode}")
        
        loss_dict['afce_loss'] = total_loss
        return loss_dict

    @torch.no_grad()
    def per_loss(self, reference_embeds, target_embeds, captions):
        F_r = self.encode_image(reference_embeds)
        F_t = self.encode_image(target_embeds)
        sim_i2t = self.inference(F_r, F_t, captions)
        loss = - (sim_i2t / self.temp).log_softmax(1).diag()
        return loss, sim_i2t.diag()

    def robust_infoNCE(self, scores, labels, pn_loss):
        eps=1e-7
        self.temp.data = torch.clamp(self.temp.data, min=1e-2)
        i2t = (scores/ self.temp).softmax(1)
        i2t = torch.clamp(i2t, min=eps, max=1-eps)
        target=torch.arange(scores.shape[0]).to(scores.device)
        clean_mask = labels.to(bool)
        noise_mask = ~clean_mask
        ploss = get_closs(i2t[clean_mask], target[clean_mask], pn_loss['positive_loss'])
        nloss = get_closs(i2t[noise_mask], target[noise_mask], pn_loss['negative_loss'])
        trade_off = pn_loss['trade_off']
        return trade_off * ploss + (1 - trade_off) * nloss

    def forward(self, samples, labels, pn_loss, warmup):
        image_embeds = samples["image"]
        target_embeds = samples["target"]
        text = samples["text_input"]
        image_embeds = self.ln_vision(image_embeds) # avoid repeated computation of laynorm in image encode
        text_tokens = self.tokenizer(
            text,
            padding="max_length",
            truncation=True,
            max_length=self.max_txt_len,
            return_tensors="pt",
        ).to(image_embeds.device)
        text_tokens = text_tokens.to(image_embeds.device)
        query_tokens = self.query_tokens.expand(image_embeds.shape[0], -1, -1)
        F_reference = self.encode_image(image_embeds, query_tokens, ln=False)
        target_embeds = self.ln_vision(target_embeds)
        F_target = self.encode_image(target_embeds, query_tokens, ln=False)
        z_target = F.normalize(self.vision_proj(F_target), dim=-1)
        loss_dict = {}
        
        # fusion encode
        z_rm = self.encode_fusion(F_reference, text_tokens)
        
        # 选择损失计算方式
        use_afce = hasattr(self, 'afce_mode') and self.afce_mode in ["FCE", "AFCE_fixed", "AFCE_dynamic"]
        
        if use_afce:
            # 编码单独的文本嵌入用于AFCE
            text_embeds = self.encode_text(text_tokens)
            # 对F_reference应用vision_proj以匹配text_embeds的维度
            F_reference_proj = F.normalize(self.vision_proj(F_reference), dim=-1)
            afce_loss_dict = self.compute_afce_loss(z_rm, z_target, F_reference_proj, text_embeds, labels)
            loss_dict.update(afce_loss_dict)
            loss_dict['lrm'] = afce_loss_dict['afce_loss']
        else:
            # 原始损失计算
            sim_r2t = torch.matmul(
                z_rm.unsqueeze(1).unsqueeze(1), z_target.permute(0, 2, 1)
            ).squeeze()
            sim_r2t, _ = sim_r2t.max(-1)
            lrm = self.robust_infoNCE(sim_r2t, labels, pn_loss)
            loss_dict['lrm'] = lrm

        return loss_dict

    @torch.no_grad()
    def inference(self, F_reference, F_target, text):
        text_tokens = self.tokenizer(
                text,
                padding="max_length",
                truncation=True,
                max_length=self.max_txt_len,
                return_tensors="pt",
            ).to(F_reference.device)
        z_rm = self.encode_fusion(F_reference, text_tokens)
        z_target = F.normalize(self.vision_proj(F_target), dim=-1)
        sim_t2q = torch.matmul(
            z_rm.unsqueeze(1).unsqueeze(1), z_target.permute(0, 2, 1)
        ).squeeze()
        sim_i2t, _ = sim_t2q.max(-1)
        return sim_i2t

    @classmethod
    def from_config(cls, cfg):
        vit_model = cfg.get("vit_model", "eva_clip_g")
        img_size = cfg.get("image_size")
        num_query_token = cfg.get("num_query_token")
        cross_attention_freq = cfg.get("cross_attention_freq", 2)

        drop_path_rate = cfg.get("drop_path_rate", 0)
        use_grad_checkpoint = cfg.get("use_grad_checkpoint", False)
        vit_precision = cfg.get("vit_precision", "fp16")
        freeze_vit = cfg.get("freeze_vit", True)

        max_txt_len = cfg.get("max_txt_len", 32)
        
        # AFCE相关配置
        afce_mode = cfg.get("afce_mode", "FCE")
        afce_p = cfg.get("afce_p", 0.5)
        afce_alpha = cfg.get("afce_alpha", 2.0)
        afce_beta = cfg.get("afce_beta", 2.0)

        model = cls(
            vit_model=vit_model,
            img_size=img_size,
            drop_path_rate=drop_path_rate,
            use_grad_checkpoint=use_grad_checkpoint,
            vit_precision=vit_precision,
            freeze_vit=freeze_vit,
            num_query_token=num_query_token,
            cross_attention_freq=cross_attention_freq,
            max_txt_len=max_txt_len,
            afce_mode=afce_mode,
            afce_p=afce_p,
            afce_alpha=afce_alpha,
            afce_beta=afce_beta,
        )
        model.load_checkpoint_from_config(cfg)

        return model
