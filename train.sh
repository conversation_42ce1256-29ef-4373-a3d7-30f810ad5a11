#!/bin/bash
# AFCE训练脚本 - 支持多种AFCE模式
# dataset: CIRR或FashionIQ
# noise_ratio: 噪声比例（简化版仍可设置，但对训练无实际影响）
# gpu: 指定要使用的GPU设备编号（0, 1, 2...）
# afce_mode: AFCE模式 (CE, FCE, AFCE_fixed, AFCE_dynamic)

# =============================================================================
# 实验配置
# =============================================================================
your_exp_name="afce_dynamic_test"
shuffle_seed=42
seed=42 
dataset="CIRR"
# 指定使用的GPU编号，注意这里直接使用数字，不要加引号
gpu=0
noise_ratio=0.0

# =============================================================================
# AFCE模式配置
# 可选值: CE, FCE, AFCE_fixed, AFCE_dynamic
# =============================================================================
afce_mode="AFCE_dynamic"

# AFCE_fixed模式参数 (当afce_mode=AFCE_fixed时生效)
afce_p=0.5

# AFCE_dynamic模式参数 (当afce_mode=AFCE_dynamic时生效)
afce_alpha=1.0
afce_beta=2.0

# =============================================================================
# 训练参数
# =============================================================================
batch_size=64
num_epochs=30
learning_rate="1e-5"
weight_decay=0.05
positive_loss="RCL"
negative_loss="None"
trade_off=1.0

# =============================================================================
# 打印配置信息
# =============================================================================
echo "=========================================="
echo "AFCE训练配置"
echo "=========================================="
echo "实验名称: ${your_exp_name}"
echo "数据集: ${dataset}"
echo "GPU设备: ${gpu}"
echo "批次大小: ${batch_size}"
echo "训练轮数: ${num_epochs}"
echo "学习率: ${learning_rate}"
echo "=========================================="
echo "AFCE配置:"
echo "模式: ${afce_mode}"

if [ "$afce_mode" = "AFCE_fixed" ]; then
    echo "固定p参数: ${afce_p}"
elif [ "$afce_mode" = "AFCE_dynamic" ]; then
    echo "Beta分布参数: alpha=${afce_alpha}, beta=${afce_beta}"
fi

echo "=========================================="
echo "开始训练..."
echo "=========================================="

# =============================================================================
# 执行训练
# =============================================================================
python src/precompute_train.py \
    --exp_name "${your_exp_name}" \
    --shuffle_seed ${shuffle_seed} \
    --seed ${seed} \
    --dataset ${dataset} \
    --noise_ratio ${noise_ratio} \
    --batch_size ${batch_size} \
    --num_epochs ${num_epochs} \
    --lr ${learning_rate} \
    --weight_decay ${weight_decay} \
    --positive_loss ${positive_loss} \
    --negative_loss ${negative_loss} \
    --trade_off ${trade_off} \
    --afce_mode ${afce_mode} \
    --afce_p ${afce_p} \
    --afce_alpha ${afce_alpha} \
    --afce_beta ${afce_beta} \
    --save_training \
    --gpu ${gpu}

# =============================================================================
# 训练完成提示
# =============================================================================
if [ $? -eq 0 ]; then
    echo "=========================================="
    echo "训练完成！"
    echo "实验名称: ${your_exp_name}"
    echo "AFCE模式: ${afce_mode}"
    echo "结果保存在: log_TME/${dataset}/${your_exp_name}/"
    echo "=========================================="
else
    echo "=========================================="
    echo "训练失败，请检查错误信息"
    echo "=========================================="
    exit 1
fi
