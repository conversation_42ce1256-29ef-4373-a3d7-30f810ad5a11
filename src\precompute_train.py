import argparse
import numpy as np
import pandas as pd
import random
import torch
import os
import time
import logging
import json

import torch.utils
import torch.utils.data
from torch.optim.lr_scheduler import OneCycleLR
from lavis.models import load_model_and_preprocess
from utility import base_path, device, params
import utility
from tqdm import tqdm
import copy
import data
from lavis.models.blip2_models.blip2_qformer_cir_image_diff_features import Blip2QformerCirImageDiffFeatures
from precompute_evaluation import evaluate_features

def set_seed(seed: int = 42, shuffle_seed: int = 42) -> None:
    np.random.seed(seed)
    random.seed(shuffle_seed)
    torch.manual_seed(seed)
    torch.cuda.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    # When running on the CuDNN backend, two further options must be set
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    # Set a fixed value for the hash seed
    os.environ["PYTHONHASHSEED"] = str(seed)
    print(f"Random seed set as {seed}")

def blip_finetune(args):
    blip_model_name = 'blip2_cir_image_diff_features'
    if args.exp_name:
        training_path = base_path / 'log_TME' / f'{args.dataset}'/ args.exp_name
    else:    
        training_path = base_path / 'log_TME' / f'{args.dataset}'/ args.timestamp
    os.makedirs(training_path, exist_ok=True)
    backbone = args.backbone
    
    # 加载模型时传入AFCE相关参数
    blip_model, vis_processors, txt_processors = load_model_and_preprocess(
        name=blip_model_name, 
        model_type=backbone, 
        is_eval=False, 
        device=device
    )
    
    # 设置AFCE参数
    if hasattr(blip_model, 'afce_mode'):
        blip_model.afce_mode = args.afce_mode
        blip_model.afce_p = args.afce_p
        blip_model.afce_alpha = args.afce_alpha
        blip_model.afce_beta = args.afce_beta
        logging.info(f"AFCE模式设置为: {args.afce_mode}")
        if args.afce_mode in ["AFCE_fixed", "AFCE_dynamic"]:
            logging.info(f"AFCE参数: p={args.afce_p}, alpha={args.afce_alpha}, beta={args.afce_beta}")
    
    update_method = getattr(blip_model, '_update_f_former', None)
    if callable(update_method):
        blip_model._update_f_former()
    preprocess = utility.targetpad_transform(target_ratio=1.25, dim=224)
    
    dataset = data.get_dataset(args.dataset, preprocess, 'train', mode='relative', noise_ratio=args.noise_ratio)
    learning_rate = args.lr
    num_epochs = args.num_epochs
    
    optimizer = torch.optim.AdamW(
        [{'params': filter(lambda p: p.requires_grad, blip_model.parameters()), 'lr': learning_rate,
          'betas': (0.9, 0.98), 'eps': 1e-7, 'weight_decay':args.weight_decay}])
    
    dataloader = torch.utils.data.DataLoader(dataset=dataset, batch_size=args.batch_size,
                                       num_workers=args.num_workers, pin_memory=True, drop_last=True, shuffle=True)
    scheduler = OneCycleLR(optimizer, max_lr=learning_rate, pct_start=1.5/num_epochs, 
                           div_factor=100., steps_per_epoch=len(dataloader), epochs=num_epochs)
    
    scaler = torch.cuda.amp.GradScaler()

    training_log_frame = pd.DataFrame()
    accuracy_list = []
    best_acc = 0
    dataset.load_image_features()
    
    valset = data.get_dataset(args.dataset, preprocess, 'val', mode='relative')
    valset.load_image_features()
    
    # 损失设置
    pn_loss = {
        'positive_loss': args.positive_loss,
        'negative_loss': args.negative_loss, 
        'trade_off': args.trade_off,
        'positive_align_loss': 'None',
        'negative_align_loss': 'None',
        'trade_off_align': 1.0,
        'warmup_loss': args.positive_loss,
        'warmup_align_loss': 'None'
    }
    
    for epoch in range(num_epochs):
        logging.info("Epoch {}/{}".format(epoch + 1, args.num_epochs))
        
        train_running_results = {'images_in_epoch': 0, 'lrm': 0.0}
        if args.afce_mode in ["FCE", "AFCE_fixed", "AFCE_dynamic"]:
            train_running_results['afce_loss'] = 0.0
        
        train_bar = tqdm(dataloader, ncols=120, mininterval=30)
        
        for reference_name, target_hard_name, captions, index in train_bar:
            reference_images = dataset.get_image_features(reference_name).to(device, non_blocking=True)
            target_images = dataset.get_image_features(target_hard_name).to(device, non_blocking=True)
            optimizer.zero_grad()
            
            # 为所有样本设置为1（表示全部为干净样本）
            labels = torch.ones(reference_images.shape[0], dtype=torch.long).to(device)
            
            if args.dataset == 'FashionIQ':
                flattened_captions = np.array(captions).T.flatten().tolist()
                captions = utility.generate_randomized_fiq_caption(flattened_captions)
            
            captions = [txt_processors['eval'](caption) for caption in captions]
            blip_model.train()
            samples = {"image": reference_images, "target": target_images, "text_input":captions}
            
            with torch.cuda.amp.autocast():
                # 传递AFCE相关参数
                loss_dict = blip_model(samples, labels, pn_loss, None)
            
            # 使用主要损失
            loss = loss_dict['lrm']
            
            scaler.scale(loss).backward()
            scaler.step(optimizer)
            scaler.update()
            scheduler.step()
            
            images_in_batch = reference_images.shape[0]
            train_running_results['lrm'] += loss_dict['lrm'].to('cpu').detach().item() * images_in_batch
            
            # 记录AFCE损失（如果存在）
            if 'afce_loss' in loss_dict:
                train_running_results['afce_loss'] += loss_dict['afce_loss'].to('cpu').detach().item() * images_in_batch
            
            train_running_results['images_in_epoch'] += images_in_batch
            
            # 更新进度条显示
            bar_content = f"lrm: {train_running_results['lrm'] / train_running_results['images_in_epoch']:.3f}"
            if 'afce_loss' in train_running_results:
                bar_content += f", afce: {train_running_results['afce_loss'] / train_running_results['images_in_epoch']:.3f}"
            train_bar.set_description(desc=f"[{epoch+1}/{num_epochs}] {bar_content}", refresh=False)
            
        # 准备损失日志
        loss_log_dict = {
            'epoch': epoch,
            'lrm': float(train_running_results['lrm'] / train_running_results['images_in_epoch'])
        }
        
        if 'afce_loss' in train_running_results:
            loss_log_dict['afce_loss'] = float(train_running_results['afce_loss'] / train_running_results['images_in_epoch'])
        
        # Training CSV logging
        training_log_frame = pd.concat([training_log_frame, pd.DataFrame(data=loss_log_dict, index=[0])])
        training_log_frame.to_csv((training_path / 'train_metrics.csv'), index=False)
        
        # evaluation
        blip_model.eval()
        print("\n" + "="*80)
        print("开始评估第 {} 个Epoch的模型".format(epoch + 1))
        logging.info(f"开始评估第 {epoch + 1} 个Epoch的模型")
        
        accuracy_dict = evaluate_features(model=blip_model, dataset=valset, text_preprocessor=txt_processors["eval"])
        cur_acc = accuracy_dict['acc']
        
        print(f"验证结果 - Epoch {epoch+1}:")
        for key, value in accuracy_dict.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
        
        if cur_acc > best_acc and args.save_training:
            best_acc = cur_acc
            logging.info(f'保存当前最佳模型 (准确率: {cur_acc:.2f})')
            print(f'保存当前最佳模型 (准确率: {cur_acc:.2f})')
            torch.save(blip_model.state_dict(), training_path / 'best_model.pth')
        accuracy_list.append(accuracy_dict['acc'])
        print("="*80 + "\n")
        
    with open('./res_acc.log', 'a+') as f:
        f.write(f'{args.timestamp}: {args.exp_name} (AFCE: {args.afce_mode})\n')
        f.write(f'{str(accuracy_dict)}\n')
    if args.save_training:
        logging.info('Save the last epoch model')
        torch.save(blip_model.state_dict(), training_path / '30_epoch_model.pth')
    return accuracy_list

def parse_args():
    parser = argparse.ArgumentParser()
    # dataset
    parser.add_argument('--dataset', type=str, default='CIRR')
    parser.add_argument('--noise_ratio', type=float, default=0.0)
    parser.add_argument('--nc_type', type=str, default='mix')
    parser.add_argument('--method', type=str, default='image_diff')
    parser.add_argument('--debug', action='store_true', help='the debug of partitioner')
    
    # basic setting
    parser.add_argument('--backbone', type=str, default='pretrain')
    parser.add_argument('--seed', type=int, default=42)
    parser.add_argument('--shuffle_seed', type=int, default=42, help='The seed for shuffle data')
    parser.add_argument('--num_workers', type=int, default=9)
    parser.add_argument('--weight_decay', type=float, default=0.05)
    parser.add_argument('--lr', type=float, default=1e-5)
    parser.add_argument('--gpu', type=str, help='The index of used gpu', default=0)
    parser.add_argument('--batch_size', type=int, default=128)
    parser.add_argument('--num_epochs', type=int, default=30)
    
    # method setting
    parser.add_argument('--positive_loss', type=str, default='RCL')
    parser.add_argument('--negative_loss', type=str, default='None')
    parser.add_argument('--trade_off', type=float, default=1.0)
    
    # AFCE相关参数
    parser.add_argument('--afce_mode', type=str, default='FCE', 
                       choices=['CE', 'FCE', 'AFCE_fixed', 'AFCE_dynamic'],
                       help='AFCE loss mode: CE (original), FCE (synthetic composition), AFCE_fixed (fixed p), AFCE_dynamic (dynamic p)')
    parser.add_argument('--afce_p', type=float, default=0.5,
                       help='固定p模式下的掩码概率参数')
    parser.add_argument('--afce_alpha', type=float, default=2.0,
                       help='动态p模式下Beta分布的alpha参数')
    parser.add_argument('--afce_beta', type=float, default=2.0,
                       help='动态p模式下Beta分布的beta参数')
    
    parser.add_argument('--save_training', action='store_true', help='save model in training.')
    
    # experiment_name
    parser.add_argument('--exp_name', type=str, default='')
    
    args = parser.parse_args()
    return args

if __name__ == '__main__':
    args = parse_args()
    
    # 设置数据集名称
    if args.dataset.lower() == 'cirr':
        args.dataset = 'CIRR'
    elif args.dataset.lower() == 'fashioniq':
        args.dataset = 'FashionIQ'
    else:
        raise ValueError(f'The name of dataset {args.dataset} is invalid.')
    
    # 获取日志路径和时间戳
    log_folder_path, timestamp = utility.get_log(args.dataset, args.exp_name)
    args.timestamp = timestamp
    
    # 确认日志文件夹已创建并有写入权限
    log_file_path = log_folder_path / 'process.log'
    print(f"日志文件路径: {log_file_path}")
    try:
        # 测试日志文件写入权限
        with open(log_file_path, 'a') as f:
            f.write(f"=== 训练开始于 {time.strftime('%Y-%m-%d %H:%M:%S')} ===\n")
            f.write(f"=== AFCE模式: {args.afce_mode} ===\n")
        print(f"日志文件创建/访问成功: {log_file_path}")
    except Exception as e:
        print(f"警告: 无法写入日志文件: {e}")
    
    # 设置GPU设备
    utility.set_device(args.gpu)
    
    # 设置随机种子
    set_seed(args.seed, args.shuffle_seed)
    
    # 初始化参数
    file_name = './log_TME/parameters.json'
    os.makedirs('./log_TME', exist_ok=True)
    utility.Params.initialize(args)
    
    # 记录参数
    logging.info('Arguments:')
    for k in args.__dict__.keys():
        logging.info(f'    {k}: {str(args.__dict__[k])}')
    
    # 开始训练
    logging.info("开始训练过程")
    accuracy_list = blip_finetune(args)
    
    # 记录结果
    this_dict = {timestamp:{'parameters': params(), 'accuracies': [round(num, 2) for num in accuracy_list], 
                            'max_acc':round(max(accuracy_list), 2), 'max_acc_epoch': int(np.argmax(accuracy_list)+1),
                            'last_epoch_acc':round(accuracy_list[-1], 2),
                            'afce_mode': args.afce_mode,  # 记录AFCE模式
                            }}
    if os.path.exists(file_name):
        with open(file_name, 'r') as json_file: 
            my_dict = json.load(json_file)
        my_dict.update(this_dict)
    else:
        my_dict = this_dict

    formatted_json_string = utility.custom_json_dumps(my_dict, indent=2)
    with open(file_name, 'w') as f:
        f.write(formatted_json_string)
        
    logging.info("训练完成")
