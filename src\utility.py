import pathlib
import os
from typing import List
import torchvision.transforms.functional as F
from torchvision.transforms import <PERSON>mpose, Resize, CenterCrop, ToTensor, Normalize, InterpolationMode
import torch
import logging
import time
import sys
from sklearn.mixture import GaussianMixture
from tqdm import tqdm
import numpy as np
import json
import copy
import random


dir_path = os.path.dirname(os.path.dirname(__file__))
base_path = pathlib.Path(dir_path)

device = torch.device('cuda')


def generate_randomized_fiq_caption(flattened_captions: List[str]) -> List[str]:
    """
    Function which randomize the FashionIQ training captions in four way: (a) cap1 and cap2 (b) cap2 and cap1 (c) cap1
    (d) cap2
    :param flattened_captions: the list of caption to randomize, note that the length of such list is 2*batch_size since
     to each triplet are associated two captions
    :return: the randomized caption list (with length = batch_size)
    """
    captions = []
    for i in range(0, len(flattened_captions), 2):
        random_num = random.random()
        if random_num < 0.25:
            captions.append(
                f"{flattened_captions[i].strip('.?, ').capitalize()} and {flattened_captions[i + 1].strip('.?, ')}")
        elif 0.25 < random_num < 0.5:
            captions.append(
                f"{flattened_captions[i + 1].strip('.?, ').capitalize()} and {flattened_captions[i].strip('.?, ')}")
        elif 0.5 < random_num < 0.75:
            captions.append(f"{flattened_captions[i].strip('.?, ').capitalize()}")
        else:
            captions.append(f"{flattened_captions[i + 1].strip('.?, ').capitalize()}")
    return captions


def set_device(i):
    global device
    logging.info(f"尝试设置GPU设备为: cuda:{i}")
    try:
        # 确保i是整数
        i = int(i)
        # 设置CUDA设备
        torch.cuda.set_device(i)
        if torch.cuda.is_available():
            device = torch.device(f'cuda:{i}')
            logging.info(f"成功设置设备为: {device}")
            logging.info(f"当前活跃的CUDA设备: {torch.cuda.current_device()}")
        else:
            device = torch.device('cpu')
            logging.info("CUDA不可用，使用CPU")
    except Exception as e:
        logging.error(f"设置GPU设备时出错: {e}")
        if torch.cuda.is_available():
            device = torch.device('cuda:0')
            logging.info(f"出错后使用默认设备: {device}")
        else:
            device = torch.device('cpu')
            logging.info("CUDA不可用，使用CPU")


def get_closs(i2t, target, loss_name=None):
    """
    计算分类损失
    
    Args:
        i2t: 预测概率分布
        target: 目标标签
        loss_name: 损失函数名称
    
    Returns:
        loss: 计算得到的损失值
    """
    loss = torch.tensor(0.).to(i2t.device)
    bs = i2t.shape[0]
    if bs == 0:
        return loss
    if loss_name == 'None' or loss_name is None:
        return loss
    if loss_name == 'RCL':
        mask = torch.ones_like(i2t).to(float).to(i2t.device)
        mask[torch.arange(bs), target] = 0.
        loss = - ((1. - i2t).log() * mask).sum() / bs
        return loss
    if loss_name == 'infoNCE':
        mask = torch.zeros_like(i2t).to(float).to(i2t.device)
        mask[torch.arange(bs), target] = 1.
        loss = - (i2t.log() * mask).sum() / bs
        return loss
    raise ValueError('loss name is invalid')

def get_aloss(left, right, loss_name=None):
    """
    计算对齐损失
    
    Args:
        left: 左侧特征
        right: 右侧特征  
        loss_name: 损失函数名称
    
    Returns:
        loss: 计算得到的损失值
    """
    bs = left.shape[0]
    loss = torch.tensor(0.).to(left.device)
    mse_criterion = torch.nn.MSELoss()
    sml1_criterion = torch.nn.SmoothL1Loss()
    if bs == 0:
        return loss
    if loss_name is None or loss_name == 'None':
        return loss
    if loss_name == 'MSE':
        loss = mse_criterion(left, right)
        return loss
    if loss_name == 'SmoothL1':
        loss = sml1_criterion(left, right)
        return loss
    raise ValueError('loss name is invalid')

def robust_mse(left, right, labels, pn_loss):
    """
    鲁棒MSE损失计算
    
    Args:
        left: 左侧特征
        right: 右侧特征
        labels: 样本标签（区分干净样本和噪声样本）
        pn_loss: 损失配置字典
    
    Returns:
        loss_dca: 鲁棒损失值
    """
    clean_mask = labels.to(bool)
    noise_mask = ~clean_mask
    ploss = get_aloss(left[clean_mask], right[clean_mask], pn_loss['positive_align_loss'])
    nloss = get_aloss(left[noise_mask], right[noise_mask], pn_loss['negative_align_loss']) 
    trade_off = pn_loss['trade_off_align']
    loss_dca = trade_off * ploss + (1-trade_off) * nloss
    return loss_dca

class TargetPad:
    """
    Pad the image if its aspect ratio is above a target ratio.
    Pad the image to match such target ratio
    """

    def __init__(self, target_ratio: float, size: int):
        """
        :param target_ratio: target ratio
        :param size: preprocessing output dimension
        """
        self.size = size
        self.target_ratio = target_ratio

    def __call__(self, image):
        w, h = image.size
        actual_ratio = max(w, h) / min(w, h)
        if actual_ratio < self.target_ratio:  # check if the ratio is above or below the target ratio
            return image
        scaled_max_wh = max(w, h) / self.target_ratio  # rescale the pad to match the target ratio
        hp = max(int((scaled_max_wh - w) / 2), 0)
        vp = max(int((scaled_max_wh - h) / 2), 0)
        padding = [hp, vp, hp, vp]
        return F.pad(image, padding, 0, 'constant')
    
def _convert_image_to_rgb(image):
    return image.convert("RGB")   

def targetpad_transform(target_ratio: float, dim: int):
    """
    CLIP-like preprocessing transform computed after using TargetPad pad
    :param target_ratio: target ratio for TargetPad
    :param dim: image output dimension
    :return: CLIP-like torchvision Compose transform
    """
    return Compose([
        TargetPad(target_ratio, dim),
        Resize(dim, interpolation=InterpolationMode.BICUBIC),
        CenterCrop(dim),
        _convert_image_to_rgb,
        ToTensor(),
        Normalize((0.48145466, 0.4578275, 0.40821073), (0.26862954, 0.26130258, 0.27577711)),
    ])
    
def get_log(dataset_name, exp_name):
    """
    设置日志系统并创建日志文件夹。
    
    Args:
        dataset_name: 数据集名称
        exp_name: 实验名称
        
    Returns:
        tuple: (日志文件夹路径, 时间戳)
    """
    # 创建基本目录结构
    if not os.path.exists(base_path /'log_TME'):
        os.makedirs(base_path / 'log_TME', exist_ok=True)
    if not os.path.exists(base_path / 'log_TME' / dataset_name):
        os.makedirs(base_path / 'log_TME' / dataset_name, exist_ok=True)
    
    # 生成时间戳和日志文件夹路径
    timestamp = time.strftime('%Y-%m-%d %H_%M_%S', time.localtime(time.time()))
    if exp_name:
        log_folder_path = base_path / 'log_TME' / dataset_name / exp_name
    else:
        log_folder_path = base_path / 'log_TME' / dataset_name / timestamp
    
    # 确保日志文件夹存在
    os.makedirs(log_folder_path, exist_ok=True)
    print(f"创建日志文件夹: {log_folder_path}")
    
    # 重置日志系统，防止多次调用添加多个处理器
    for handler in logging.root.handlers[:]:
        logging.root.removeHandler(handler)
    
    # 设置日志格式
    log_format = '%(asctime)s: %(message)s'
    logging.basicConfig(
        level=logging.INFO,
        format=log_format,
        datefmt='%Y-%m-%d %H:%M:%S',
        handlers=[
            logging.StreamHandler(sys.stdout),  # 输出到控制台
        ]
    )
    
    # 添加文件处理器
    try:
        log_file_path = log_folder_path / 'process.log'
        print(f"日志将被保存到: {log_file_path}")
        file_handler = logging.FileHandler(log_file_path, mode='a')
        file_handler.setFormatter(logging.Formatter(log_format, datefmt='%Y-%m-%d %H:%M:%S'))
        logging.getLogger().addHandler(file_handler)
        logging.info(f"=== 训练开始于 {timestamp} ===")
    except Exception as e:
        print(f"警告: 创建日志文件处理器失败: {e}")
    
    return log_folder_path, timestamp

def get_log_simple(file_path):
    """
    简单日志设置
    
    Args:
        file_path: 日志文件路径
        
    Returns:
        file_path: 返回日志文件路径
    """
    log_format = '%(asctime)s: %(message)s'
    date_format = '%Y-%m-%d-%H-%M-%S'
    logging.basicConfig(stream=sys.stdout, level=logging.INFO, format=log_format, datefmt=date_format)
    os.makedirs(os.path.dirname(file_path), exist_ok=True)
    fh = logging.FileHandler(file_path)
    fh.setFormatter(logging.Formatter(log_format, datefmt=date_format))
    logging.getLogger().addHandler(fh)
    return file_path

class Params:
    """
    参数管理类，支持AFCE相关参数
    """
    bool_initialize = False
    
    @staticmethod
    def initialize(args):
        global params
        if Params.bool_initialize:
            raise ValueError('params have been initialized.')
        if args is None:
            raise ValueError('params list should not be None')
        print('Initialize params')
        params.set_params(args)
        Params.bool_initialize = True
    
    def __init__(self):
        pass
    
    def set_params(self, args):
        # basic params:
        self.dataset = args.dataset
        self.method = getattr(args, 'method', 'image_diff')
        self.noise_ratio = args.noise_ratio
        self.nc_type = getattr(args, 'nc_type', 'mix')

        self.save_training = args.save_training
        
        # basic setting
        self.backbone = args.backbone
        self.num_workers = args.num_workers
        self.weight_decay = args.weight_decay
        self.lr = args.lr
        self.batch_size = args.batch_size
        self.num_epochs = args.num_epochs
        self.seed = args.seed
        self.shuffle_seed = args.shuffle_seed
        
        self.timestamp = args.timestamp
        self.debug = getattr(args, 'debug', False)
        
        # method setting - 损失设置
        self.pn_loss = {}
        self.pn_loss['positive_loss'] = args.positive_loss
        self.pn_loss['negative_loss'] = args.negative_loss
        self.pn_loss['trade_off'] = args.trade_off
        
        # 为缺失的参数设置默认值
        self.pn_loss['positive_align_loss'] = 'None'
        self.pn_loss['negative_align_loss'] = 'None'
        self.pn_loss['trade_off_align'] = 1.0
        self.pn_loss['warmup_loss'] = args.positive_loss
        self.pn_loss['warmup_align_loss'] = 'None'
        
        # AFCE相关参数
        self.afce_mode = getattr(args, 'afce_mode', 'FCE')
        self.afce_p = getattr(args, 'afce_p', 0.5)
        self.afce_alpha = getattr(args, 'afce_alpha', 2.0)
        self.afce_beta = getattr(args, 'afce_beta', 2.0)
        
        # 为缺失的损失权重设置默认值
        self.lrm = 1.0
        self.lpm = 0.0
        self.lsa = 0.0
        self.lrd = 0.0
        
        # 为缺失的warmup参数设置默认值
        self.warmup_qformer = 0
        self.warmup_proj = 0
        self.warmup_last = 0
        self.warmup_epoch = 0
        
        # 为缺失的partitioner参数设置默认值
        self.partitioner = 'all_positive'
        self.split_type = 'loss'
        self.threshold = 0.5
        
        # experiment_name
        self.exp_name = args.exp_name
        
    def __call__(self):
        display_dict = copy.deepcopy(self.__dict__)
        keys_to_remove = ['num_workers', 'timestamp']
        for key in keys_to_remove:
            if key in display_dict:
                del display_dict[key]
        return display_dict
    
params = Params()

class Partitioner:
    """
    样本分区器，用于区分干净样本和噪声样本
    """
    
    def __init__(self, type, split, threshold=0.5, timestamp=None, epoch=None, dataset_name=None):
        self.type = type
        self.split = split
        self.threshold = threshold
        self.timestamp = timestamp
        self.epoch = epoch
        self.dataset_name = dataset_name
        
    def fit_features(self, model, trainloader, txt_processors, debug=False):
        """
        使用预计算特征进行样本分区
        
        Args:
            model: 训练模型
            trainloader: 训练数据加载器
            txt_processors: 文本处理器
            debug: 调试模式
            
        Returns:
            pred: 样本预测标签（1为干净样本，0为噪声样本）
        """
        dataset = trainloader.dataset
        if self.type == 'all_positive':
            logging.info('no partition, all positive')
            return torch.ones(len(dataset)) # all clean
        
        logging.info('fitting partitioner...')
        model.eval()
        data_size = len(dataset)
        loss = torch.zeros(data_size)
        sim = torch.zeros(data_size)
        
        for reference_name, target_name, captions, index in tqdm(trainloader, ncols=150, mininterval=30):
            reference_images = dataset.get_image_features(reference_name).to(device, non_blocking=True)
            target_images = dataset.get_image_features(target_name).to(device, non_blocking=True)
            
            if self.dataset_name == 'FashionIQ':
                flattened_captions = np.array(captions).T.flatten().tolist()
                captions = generate_randomized_fiq_caption(flattened_captions)
            
            captions = [txt_processors['eval'](caption) for caption in captions]
            l, s = model.per_loss(reference_images, target_images, captions)
            
            for b in range(l.size(0)):
                loss[index[b]] = l[b]
                sim[index[b]] = s[b]
                
        self.losses = (loss-loss.min())/(loss.max()-loss.min())
        self.sims = (sim-sim.min())/(sim.max()-sim.min())
        self.pred = self.get_pred(self.type, debug=debug)
        return self.pred
            
    def fit(self, model, trainloader, txt_processors):
        """
        不使用预计算特征的样本分区方法（遗留代码）
        
        Args:
            model: 训练模型
            trainloader: 训练数据加载器
            txt_processors: 文本处理器
            
        Returns:
            pred: 样本预测标签
        """
        if self.type == 'all_positive':
            logging.info('no partition, all positive')
            return torch.ones(len(trainloader.dataset)) # all clean
        
        logging.info('fitting partitioner...')
        model.eval()
        data_size = len(trainloader.dataset)
        loss = torch.zeros(data_size)
        sim = torch.zeros(data_size)
        
        with tqdm(total=len(trainloader), mininterval=30) as t:
            for i, data in enumerate(trainloader):
                reference_image = data['source_img_data'].to(device, non_blocking=True)
                target_image = data['target_img_data'].to(device, non_blocking=True)
                captions = data['mod']['str']
                
                if self.dataset_name == 'FashionIQ':
                    flattened_captions = np.array(captions).T.flatten().tolist()
                    captions = generate_randomized_fiq_caption(flattened_captions)
                
                captions = [txt_processors['eval'](caption) for caption in captions]
                index = data['index']
                l, s = model.per_loss(reference_image, target_image, captions)
                
                for b in range(l.size(0)):
                    loss[index[b]] = l[b]
                    sim[index[b]] = s[b]
                t.update()
                
        self.losses = (loss-loss.min())/(loss.max()-loss.min())
        self.sims = (sim-sim.min())/(sim.max()-sim.min())
        self.pred = self.get_pred(self.type)       
        return self.pred
    
    def get_pred(self, type, threshold=None, debug=False):
        """
        根据损失值或相似度生成样本预测标签
        
        Args:
            type: 分区方法类型
            threshold: 阈值
            debug: 调试模式
            
        Returns:
            pred: 样本预测标签
        """
        type = type.lower()
        if threshold is None:
            threshold = self.threshold
            
        if type.lower() == 'gmm':
            input_loss = self.losses.reshape(-1,1) 
            input_sim = self.sims.reshape(-1,1)
            input_data = input_loss if self.split == 'loss' else input_sim
            
            # 高斯混合模型概率计算
            gmm = GaussianMixture(n_components=2, max_iter=10, tol=1e-2, reg_covar=5e-4)
            gmm.fit(input_data.cpu().numpy())
            clean_component_idx = gmm.means_.argmin() if self.split == 'loss' else gmm.means_.argmax()
            self.prob = torch.Tensor(gmm.predict_proba(input_data.cpu().numpy())[:, clean_component_idx])
            
            self.pred = (self.prob > threshold) + 0
            
            if debug:
                save_path = f'partitioner_log/{self.timestamp}'
                if not os.path.exists(save_path):
                    os.makedirs(save_path)
                torch.save(self.losses, f'{save_path}/loss_{self.epoch}.pth')
                torch.save(self.sims, f'{save_path}/sim_{self.epoch}.pth')
                torch.save(self.prob, f'{save_path}/prob_{self.epoch}.pth')
                exit(0)
                
            area_num = torch.histc(torch.tensor(self.prob), bins=10, min=0.0, max=1.0).to(torch.int).tolist()
            logging.info(f'The counts in the equal areas are: {area_num}')
            clean_pro = self.pred.sum().item() / self.pred.shape[0]
            logging.info(f'the proportion of clean samples are {clean_pro}')
            return self.pred
            
        elif type == 'direct':
            if self.split == 'loss':
                input_data = self.losses
            elif self.split == 'sim':
                input_data = self.sims
            else:
                raise ValueError(f"the parameter split is invalid.")
            self.pred = (input_data < threshold) + 0
            self.prob = self.pred
            print('the proportion of clean samples are ', self.pred.sum().item() / self.pred.shape[0])
            return self.pred
            
        elif type == 'percent':
            if self.split == 'loss':
                input_data = self.losses
            elif self.split == 'sim':
                input_data = self.sims
            else:
                raise ValueError(f"the parameter split is invalid.")
            noisy_indices = input_data.argsort(descending=True)[:int(threshold * input_data.shape[0])]
            self.pred = torch.ones_like(input_data)
            self.pred[noisy_indices] = 0
            self.prob = self.pred
            print('the proportion of clean samples are ', self.pred.sum().item() / self.pred.shape[0])
            return self.pred
        else:
            raise ValueError(f"the parameter type is invalid.")
        
    def get_prob(self):
        """
        获取样本概率
        
        Returns:
            prob: 样本为干净样本的概率
        """
        if not hasattr(self, 'prob') or self.prob is None:
            raise KeyError('prob does not exist')
        else:
            return self.prob

def afce_loss_info(afce_mode, afce_p=None, afce_alpha=None, afce_beta=None):
    """
    生成AFCE损失的信息描述
    
    Args:
        afce_mode: AFCE模式
        afce_p: 固定p参数
        afce_alpha: Beta分布alpha参数
        afce_beta: Beta分布beta参数
        
    Returns:
        info_str: AFCE配置信息字符串
    """
    info_lines = [f"AFCE Mode: {afce_mode}"]
    
    if afce_mode == "CE":
        info_lines.append("Using original Composition Examples (CE) with query-level negatives")
    elif afce_mode == "FCE":
        info_lines.append("Using Fixed Composition Examples (FCE) with component-level negatives")
    elif afce_mode == "AFCE_fixed":
        info_lines.append(f"Using Augmented FCE with fixed p={afce_p}")
        info_lines.append("Element-wise mask generation with fixed Bernoulli parameter")
    elif afce_mode == "AFCE_dynamic":
        info_lines.append(f"Using Augmented FCE with dynamic p~Beta({afce_alpha}, {afce_beta})")
        info_lines.append("Element-wise mask generation with dynamic Bernoulli parameter")
    
    return "\n".join(info_lines)

def validate_afce_params(afce_mode, afce_p, afce_alpha, afce_beta):
    """
    验证AFCE参数的有效性
    
    Args:
        afce_mode: AFCE模式
        afce_p: 固定p参数
        afce_alpha: Beta分布alpha参数
        afce_beta: Beta分布beta参数
        
    Raises:
        ValueError: 当参数无效时抛出异常
    """
    valid_modes = ["CE", "FCE", "AFCE_fixed", "AFCE_dynamic"]
    if afce_mode not in valid_modes:
        raise ValueError(f"Invalid afce_mode: {afce_mode}. Must be one of {valid_modes}")
    
    if afce_mode in ["AFCE_fixed", "FCE"] and not (0.0 <= afce_p <= 1.0):
        raise ValueError(f"afce_p must be between 0.0 and 1.0, got {afce_p}")
    
    if afce_mode == "AFCE_dynamic":
        if afce_alpha <= 0 or afce_beta <= 0:
            raise ValueError(f"Beta distribution parameters must be positive, got alpha={afce_alpha}, beta={afce_beta}")

def custom_json_dumps(data, indent=4):
    """
    自定义JSON序列化函数，支持更好的格式化
    
    Args:
        data: 要序列化的数据
        indent: 缩进空格数
        
    Returns:
        formatted_json: 格式化的JSON字符串
    """
    def serialize(obj, indent_level=0):
        if isinstance(obj, dict):
            items = []
            for key, value in obj.items():
                items.append(f'\n{" " * indent * (indent_level + 1)}"{key}": {serialize(value, indent_level + 1)}')
            return f'{{{",".join(items)}\n{" " * indent * indent_level}}}'
        elif isinstance(obj, list):
            items = [json.dumps(item, indent=0) for item in obj]
            return f'[{", ".join(items)}]'
        else:
            return json.dumps(obj)

    return serialize(data)

def save_experiment_config(args, training_path):
    """
    保存实验配置到文件
    
    Args:
        args: 命令行参数
        training_path: 训练路径
    """
    config_dict = {
        'experiment_info': {
            'timestamp': args.timestamp,
            'dataset': args.dataset,
            'exp_name': args.exp_name,
        },
        'model_config': {
            'backbone': args.backbone,
            'method': getattr(args, 'method', 'image_diff'),
        },
        'training_config': {
            'num_epochs': args.num_epochs,
            'batch_size': args.batch_size,
            'lr': args.lr,
            'weight_decay': args.weight_decay,
            'seed': args.seed,
            'shuffle_seed': args.shuffle_seed,
        },
        'loss_config': {
            'positive_loss': args.positive_loss,
            'negative_loss': args.negative_loss,
            'trade_off': args.trade_off,
        },
        'afce_config': {
            'afce_mode': getattr(args, 'afce_mode', 'FCE'),
            'afce_p': getattr(args, 'afce_p', 0.5),
            'afce_alpha': getattr(args, 'afce_alpha', 2.0),
            'afce_beta': getattr(args, 'afce_beta', 2.0),
        },
        'data_config': {
            'noise_ratio': args.noise_ratio,
            'nc_type': getattr(args, 'nc_type', 'mix'),
        }
    }
    
    config_file = training_path / 'experiment_config.json'
    with open(config_file, 'w') as f:
        json.dump(config_dict, f, indent=2, ensure_ascii=False)
    
    logging.info(f"实验配置已保存到: {config_file}")

def load_experiment_config(config_path):
    """
    从文件加载实验配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        config_dict: 实验配置字典
    """
    try:
        with open(config_path, 'r') as f:
            config_dict = json.load(f)
        logging.info(f"实验配置已从 {config_path} 加载")
        return config_dict
    except FileNotFoundError:
        logging.warning(f"配置文件 {config_path} 不存在")
        return None
    except json.JSONDecodeError as e:
        logging.error(f"配置文件 {config_path} 格式错误: {e}")
        return None

def print_afce_summary(args):
    """
    打印AFCE配置摘要
    
    Args:
        args: 命令行参数
    """
    print("\n" + "="*60)
    print("AFCE Configuration Summary")
    print("="*60)
    print(f"Mode: {getattr(args, 'afce_mode', 'FCE')}")
    
    if hasattr(args, 'afce_mode'):
        if args.afce_mode == "CE":
            print("- Original Composition Examples")
            print("- Query-level negative sampling")
        elif args.afce_mode == "FCE":
            print("- Fixed Composition Examples")
            print("- Component-level negative sampling")
        elif args.afce_mode == "AFCE_fixed":
            print(f"- Augmented FCE with fixed p={args.afce_p}")
            print("- Element-wise Bernoulli mask with fixed probability")
        elif args.afce_mode == "AFCE_dynamic":
            print(f"- Augmented FCE with dynamic p~Beta({args.afce_alpha}, {args.afce_beta})")
            print("- Element-wise Bernoulli mask with Beta-sampled probability")
    
    print("="*60 + "\n")

def compute_afce_statistics(synthetic_comps, augmented_comps=None):
    """
    计算AFCE相关统计信息
    
    Args:
        synthetic_comps: 合成组合样本
        augmented_comps: 增强合成组合样本（可选）
        
    Returns:
        stats_dict: 统计信息字典
    """
    stats_dict = {}
    
    if synthetic_comps is not None and synthetic_comps.numel() > 0:
        stats_dict['synthetic_mean'] = synthetic_comps.mean().item()
        stats_dict['synthetic_std'] = synthetic_comps.std().item()
        stats_dict['synthetic_num_samples'] = synthetic_comps.shape[0]
    
    if augmented_comps is not None and augmented_comps.numel() > 0:
        stats_dict['augmented_mean'] = augmented_comps.mean().item()
        stats_dict['augmented_std'] = augmented_comps.std().item()
        stats_dict['augmented_num_samples'] = augmented_comps.shape[0]
        
        # 计算增强样本与原始合成样本的差异
        if synthetic_comps is not None and synthetic_comps.numel() > 0:
            if synthetic_comps.shape == augmented_comps.shape:
                diff = torch.norm(augmented_comps - synthetic_comps, dim=1).mean()
                stats_dict['augmentation_magnitude'] = diff.item()
    
    return stats_dict

def log_training_progress(epoch, total_epochs, loss_dict, accuracy_dict=None):
    """
    记录训练进度
    
    Args:
        epoch: 当前轮次
        total_epochs: 总轮次
        loss_dict: 损失字典
        accuracy_dict: 准确率字典（可选）
    """
    progress_msg = f"Epoch [{epoch+1}/{total_epochs}]"
    
    # 记录损失信息
    loss_info = []
    for loss_name, loss_value in loss_dict.items():
        if isinstance(loss_value, (int, float)):
            loss_info.append(f"{loss_name}: {loss_value:.4f}")
    
    if loss_info:
        progress_msg += f" - Losses: {', '.join(loss_info)}"
    
    # 记录准确率信息
    if accuracy_dict:
        acc_info = []
        for acc_name, acc_value in accuracy_dict.items():
            if isinstance(acc_value, (int, float)):
                acc_info.append(f"{acc_name}: {acc_value:.2f}")
        
        if acc_info:
            progress_msg += f" - Metrics: {', '.join(acc_info)}"
    
    logging.info(progress_msg)

def create_afce_negative_sampling_mask(batch_size, exclude_diagonal=True):
    """
    创建AFCE负采样掩码
    
    Args:
        batch_size: 批次大小
        exclude_diagonal: 是否排除对角线元素
        
    Returns:
        mask: 负采样掩码 [B, B*(B-1)]
    """
    mask = torch.ones((batch_size, batch_size * (batch_size - 1)), dtype=torch.bool)
    
    if exclude_diagonal:
        # 排除自身配对
        for i in range(batch_size):
            start_idx = i * (batch_size - 1)
            end_idx = start_idx + (batch_size - 1)
            # 这里可以添加更复杂的掩码逻辑
            pass
    
    return mask

def compute_beta_distribution_stats(alpha, beta, num_samples=10000):
    """
    计算Beta分布的统计信息
    
    Args:
        alpha: Beta分布的alpha参数
        beta: Beta分布的beta参数
        num_samples: 采样数量
        
    Returns:
        stats: Beta分布统计信息
    """
    samples = np.random.beta(alpha, beta, num_samples)
    
    stats = {
        'mean': np.mean(samples),
        'std': np.std(samples),
        'median': np.median(samples),
        'min': np.min(samples),
        'max': np.max(samples),
        'q25': np.percentile(samples, 25),
        'q75': np.percentile(samples, 75),
    }
    
    return stats

def format_afce_loss_log(loss_dict, afce_mode):
    """
    格式化AFCE损失日志
    
    Args:
        loss_dict: 损失字典
        afce_mode: AFCE模式
        
    Returns:
        formatted_log: 格式化的日志字符串
    """
    log_parts = []
    
    if 'lrm' in loss_dict:
        log_parts.append(f"LRM: {loss_dict['lrm']:.4f}")
    
    if 'afce_loss' in loss_dict:
        log_parts.append(f"AFCE({afce_mode}): {loss_dict['afce_loss']:.4f}")
    
    if 'l0_loss' in loss_dict:
        log_parts.append(f"L0: {loss_dict['l0_loss']:.4f}")
    
    if 'l1_loss' in loss_dict:
        log_parts.append(f"L1: {loss_dict['l1_loss']:.4f}")
    
    if 'l2_loss' in loss_dict:
        log_parts.append(f"L2: {loss_dict['l2_loss']:.4f}")
    
    if 'l3_loss' in loss_dict:
        log_parts.append(f"L3: {loss_dict['l3_loss']:.4f}")
    
    return " | ".join(log_parts)

def get_afce_hyperparameter_suggestions(dataset_name):
    """
    根据数据集获取AFCE超参数建议
    
    Args:
        dataset_name: 数据集名称
        
    Returns:
        suggestions: 超参数建议字典
    """
    suggestions = {
        'CIRR': {
            'afce_p': 0.5,
            'afce_alpha': 2.0,
            'afce_beta': 2.0,
            'recommended_mode': 'FCE'
        },
        'FashionIQ': {
            'afce_p': 0.6,
            'afce_alpha': 1.5,
            'afce_beta': 1.5,
            'recommended_mode': 'AFCE_dynamic'
        }
    }
    
    return suggestions.get(dataset_name, suggestions['CIRR'])

def monitor_afce_training(loss_history, window_size=10):
    """
    监控AFCE训练过程
    
    Args:
        loss_history: 损失历史列表
        window_size: 滑动窗口大小
        
    Returns:
        monitoring_info: 监控信息字典
    """
    if len(loss_history) < window_size:
        return {'status': 'insufficient_data'}
    
    recent_losses = loss_history[-window_size:]
    trend = 'decreasing' if recent_losses[-1] < recent_losses[0] else 'increasing'
    
    monitoring_info = {
        'status': 'normal',
        'recent_mean': np.mean(recent_losses),
        'recent_std': np.std(recent_losses),
        'trend': trend,
        'convergence_indicator': np.std(recent_losses) < 0.001
    }
    
    return monitoring_info

def save_afce_experiment_results(results_dict, save_path):
    """
    保存AFCE实验结果
    
    Args:
        results_dict: 结果字典
        save_path: 保存路径
    """
    results_file = save_path / 'afce_results.json'
    
    # 确保所有numpy类型都能被序列化
    def convert_numpy(obj):
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        return obj
    
    # 递归转换字典中的numpy类型
    def clean_dict(d):
        if isinstance(d, dict):
            return {k: clean_dict(v) for k, v in d.items()}
        elif isinstance(d, list):
            return [clean_dict(item) for item in d]
        else:
            return convert_numpy(d)
    
    cleaned_results = clean_dict(results_dict)
    
    with open(results_file, 'w') as f:
        json.dump(cleaned_results, f, indent=2, ensure_ascii=False)
    
    logging.info(f"AFCE实验结果已保存到: {results_file}")

def print_afce_summary(args):
    """
    打印AFCE配置摘要
    
    Args:
        args: 命令行参数
    """
    print("\n" + "="*60)
    print("AFCE Configuration Summary")
    print("="*60)
    print(f"Mode: {getattr(args, 'afce_mode', 'FCE')}")
    
    if hasattr(args, 'afce_mode'):
        if args.afce_mode == "CE":
            print("- Original Composition Examples")
            print("- Query-level negative sampling")
        elif args.afce_mode == "FCE":
            print("- Fixed Composition Examples")  
            print("- Component-level negative sampling")
        elif args.afce_mode == "AFCE_fixed":
            print(f"- Augmented FCE with fixed p={args.afce_p}")
            print("- Element-wise Bernoulli mask with fixed probability")
        elif args.afce_mode == "AFCE_dynamic":
            print(f"- Augmented FCE with dynamic p~Beta({args.afce_alpha}, {args.afce_beta})")
            print("- Element-wise Bernoulli mask with Beta-sampled probability")
            
            # 显示Beta分布统计信息
            beta_stats = compute_beta_distribution_stats(args.afce_alpha, args.afce_beta)
            print(f"- Beta distribution stats: mean={beta_stats['mean']:.3f}, std={beta_stats['std']:.3f}")
    
    print("="*60 + "\n")

def validate_training_configuration(args):
    """
    验证训练配置的合理性
    
    Args:
        args: 命令行参数
        
    Returns:
        validation_result: 验证结果字典
    """
    warnings = []
    errors = []
    
    # 验证基本参数
    if args.batch_size <= 0:
        errors.append("batch_size must be positive")
    
    if args.lr <= 0:
        errors.append("learning rate must be positive")
    
    if args.num_epochs <= 0:
        errors.append("num_epochs must be positive")
    
    # 验证AFCE参数
    if hasattr(args, 'afce_mode'):
        try:
            validate_afce_params(args.afce_mode, 
                                getattr(args, 'afce_p', 0.5),
                                getattr(args, 'afce_alpha', 2.0), 
                                getattr(args, 'afce_beta', 2.0))
        except ValueError as e:
            errors.append(f"AFCE parameter error: {str(e)}")
    
    # AFCE模式特定的警告
    if hasattr(args, 'afce_mode'):
        if args.afce_mode in ["AFCE_fixed", "AFCE_dynamic"] and args.batch_size < 4:
            warnings.append("Small batch size may limit AFCE effectiveness")
        
        if args.afce_mode == "AFCE_dynamic":
            beta_stats = compute_beta_distribution_stats(
                getattr(args, 'afce_alpha', 2.0), 
                getattr(args, 'afce_beta', 2.0)
            )
            if beta_stats['std'] < 0.1:
                warnings.append("Low Beta distribution variance may limit augmentation diversity")
    
    return {
        'valid': len(errors) == 0,
        'errors': errors,
        'warnings': warnings
    }

def setup_afce_logging(training_path, afce_mode):
    """
    设置AFCE特定的日志记录
    
    Args:
        training_path: 训练路径
        afce_mode: AFCE模式
    """
    afce_log_path = training_path / 'afce_training.log'
    
    # 创建AFCE专用的日志记录器
    afce_logger = logging.getLogger('afce')
    afce_logger.setLevel(logging.INFO)
    
    # 避免重复添加处理器
    if not afce_logger.handlers:
        afce_handler = logging.FileHandler(afce_log_path)
        afce_formatter = logging.Formatter('%(asctime)s - AFCE - %(message)s')
        afce_handler.setFormatter(afce_formatter)
        afce_logger.addHandler(afce_handler)
    
    afce_logger.info(f"AFCE training started with mode: {afce_mode}")
    return afce_logger

def cleanup_afce_resources():
    """
    清理AFCE相关资源
    """
    # 清理AFCE日志记录器
    afce_logger = logging.getLogger('afce')
    for handler in afce_logger.handlers[:]:
        handler.close()
        afce_logger.removeHandler(handler)

def get_device_info():
    """
    获取设备信息
    
    Returns:
        device_info: 设备信息字典
    """
    device_info = {
        'device': str(device),
        'cuda_available': torch.cuda.is_available(),
    }
    
    if torch.cuda.is_available():
        device_info.update({
            'cuda_device_count': torch.cuda.device_count(),
            'current_device': torch.cuda.current_device(),
            'device_name': torch.cuda.get_device_name(),
            'memory_allocated': torch.cuda.memory_allocated(),
            'memory_reserved': torch.cuda.memory_reserved(),
        })
    
    return device_info

def log_system_info():
    """
    记录系统信息
    """
    device_info = get_device_info()
    logging.info("System Information:")
    for key, value in device_info.items():
        logging.info(f"  {key}: {value}")

def create_experiment_summary(args, accuracy_list, training_time=None):
    """
    创建实验摘要
    
    Args:
        args: 命令行参数
        accuracy_list: 准确率列表
        training_time: 训练时间（秒）
        
    Returns:
        summary_dict: 实验摘要字典
    """
    summary_dict = {
        'experiment_id': args.timestamp,
        'dataset': args.dataset,
        'afce_mode': getattr(args, 'afce_mode', 'FCE'),
        'final_accuracy': round(accuracy_list[-1], 4) if accuracy_list else 0.0,
        'best_accuracy': round(max(accuracy_list), 4) if accuracy_list else 0.0,
        'best_epoch': int(np.argmax(accuracy_list) + 1) if accuracy_list else 0,
        'total_epochs': args.num_epochs,
        'convergence_epochs': len(accuracy_list),
    }
    
    if training_time:
        summary_dict['training_time_seconds'] = training_time
        summary_dict['time_per_epoch'] = training_time / len(accuracy_list) if accuracy_list else 0
    
    # AFCE特定信息
    if hasattr(args, 'afce_mode'):
        summary_dict['afce_config'] = {
            'mode': args.afce_mode,
            'p': getattr(args, 'afce_p', 0.5),
            'alpha': getattr(args, 'afce_alpha', 2.0),
            'beta': getattr(args, 'afce_beta', 2.0),
        }
    
    return summary_dict

def backup_model_checkpoint(model_path, backup_dir, max_backups=5):
    """
    备份模型检查点
    
    Args:
        model_path: 模型文件路径
        backup_dir: 备份目录
        max_backups: 最大备份数量
    """
    if not os.path.exists(model_path):
        return
    
    os.makedirs(backup_dir, exist_ok=True)
    
    # 生成备份文件名
    timestamp = time.strftime('%Y%m%d_%H%M%S')
    backup_name = f"model_backup_{timestamp}.pth"
    backup_path = os.path.join(backup_dir, backup_name)
    
    # 复制模型文件
    import shutil
    shutil.copy2(model_path, backup_path)
    
    # 清理旧备份
    backup_files = [f for f in os.listdir(backup_dir) if f.startswith('model_backup_')]
    backup_files.sort()
    
    while len(backup_files) > max_backups:
        old_backup = os.path.join(backup_dir, backup_files.pop(0))
        os.remove(old_backup)
    
    logging.info(f"模型备份已保存到: {backup_path}")

def calculate_memory_usage():
    """
    计算内存使用情况
    
    Returns:
        memory_info: 内存信息字典
    """
    memory_info = {}
    
    if torch.cuda.is_available():
        memory_info['gpu_memory_allocated'] = torch.cuda.memory_allocated() / 1024**2  # MB
        memory_info['gpu_memory_reserved'] = torch.cuda.memory_reserved() / 1024**2   # MB
        memory_info['gpu_max_memory_allocated'] = torch.cuda.max_memory_allocated() / 1024**2  # MB
    
    # 系统内存使用（如果psutil可用）
    try:
        import psutil
        process = psutil.Process()
        memory_info['cpu_memory_mb'] = process.memory_info().rss / 1024**2
        memory_info['cpu_memory_percent'] = process.memory_percent()
    except ImportError:
        pass
    
    return memory_info

def log_memory_usage(prefix=""):
    """
    记录内存使用情况
    
    Args:
        prefix: 日志前缀
    """
    memory_info = calculate_memory_usage()
    memory_log = f"{prefix}Memory Usage:"
    
    for key, value in memory_info.items():
        if isinstance(value, float):
            memory_log += f" {key}={value:.1f}MB"
        else:
            memory_log += f" {key}={value}"
    
    logging.info(memory_log)

def ensure_reproducibility(seed):
    """
    确保实验的可重现性
    
    Args:
        seed: 随机种子
    """
    # 设置所有随机种子
    np.random.seed(seed)
    random.seed(seed)
    torch.manual_seed(seed)
    
    if torch.cuda.is_available():
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        
        # 确保CUDA操作的确定性
        torch.backends.cudnn.deterministic = True
        torch.backends.cudnn.benchmark = False
    
    # 设置Python哈希种子
    os.environ['PYTHONHASHSEED'] = str(seed)
    
    logging.info(f"随机种子已设置为: {seed}，确保实验可重现性")

def create_results_comparison_table(results_list, save_path=None):
    """
    创建结果对比表
    
    Args:
        results_list: 结果列表
        save_path: 保存路径（可选）
        
    Returns:
        comparison_df: 对比数据框
    """
    comparison_data = []
    
    for result in results_list:
        row = {
            'timestamp': result.get('timestamp', 'unknown'),
            'afce_mode': result.get('afce_mode', 'unknown'),
            'dataset': result.get('dataset', 'unknown'),
            'best_accuracy': result.get('best_accuracy', 0.0),
            'final_accuracy': result.get('final_accuracy', 0.0),
            'best_epoch': result.get('best_epoch', 0),
        }
        
        # 添加AFCE特定参数
        if 'afce_config' in result:
            afce_config = result['afce_config']
            row['afce_p'] = afce_config.get('p', 'N/A')
            row['afce_alpha'] = afce_config.get('alpha', 'N/A')
            row['afce_beta'] = afce_config.get('beta', 'N/A')
        
        comparison_data.append(row)
    
    comparison_df = pd.DataFrame(comparison_data)
    
    if save_path:
        comparison_df.to_csv(save_path / 'afce_results_comparison.csv', index=False)
        logging.info(f"结果对比表已保存到: {save_path}")
    
    return comparison_df

# 全局变量初始化
def initialize_global_variables():
    """
    初始化全局变量
    """
    global params
    if not hasattr(params, 'bool_initialize'):
        params = Params()

# 在模块加载时初始化
initialize_global_variables()
